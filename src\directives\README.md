# Vue 3 自定义指令

## v-resize 拖拽调整大小指令

### 功能特性

- ✅ 支持8个方向的拖拽调整：上、下、左、右、左上、右上、左下、右下
- ✅ 可配置最小/最大尺寸限制
- ✅ 支持保持宽高比
- ✅ 可自定义手柄大小和显示状态
- ✅ 提供调整大小回调函数
- ✅ 支持API方法调用
- ✅ 响应式设计，支持鼠标和触摸操作

### 基础用法

```vue
<template>
  <div 
    v-resize="{
      minWidth: 100,
      minHeight: 100,
      onResize: handleResize
    }"
    style="width: 300px; height: 200px; border: 1px solid #ccc;"
  >
    可调整大小的内容
  </div>
</template>

<script setup>
const handleResize = (data) => {
  console.log('新尺寸:', data.width, data.height)
  console.log('拖拽方向:', data.direction)
}
</script>
```

### 配置选项

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `minWidth` | Number | 100 | 最小宽度 |
| `minHeight` | Number | 100 | 最小高度 |
| `maxWidth` | Number | null | 最大宽度，null表示无限制 |
| `maxHeight` | Number | null | 最大高度，null表示无限制 |
| `handles` | Array | ['se', 'e', 's'] | 启用的拖拽手柄方向 |
| `handleSize` | Number | 8 | 手柄大小（像素） |
| `preserveAspectRatio` | Boolean | false | 是否保持宽高比 |
| `showHandles` | Boolean | true | 是否显示手柄 |
| `onResize` | Function | null | 调整大小时的回调函数 |

### 手柄方向说明

- `n`: 上边
- `ne`: 右上角
- `e`: 右边
- `se`: 右下角
- `s`: 下边
- `sw`: 左下角
- `w`: 左边
- `nw`: 左上角

### 高级用法

#### 1. 全方向调整

```vue
<div 
  v-resize="{
    handles: ['n', 'ne', 'e', 'se', 's', 'sw', 'w', 'nw'],
    minWidth: 200,
    minHeight: 150
  }"
>
  内容
</div>
```

#### 2. 保持宽高比

```vue
<div 
  v-resize="{
    handles: ['se', 'ne', 'sw', 'nw'],
    preserveAspectRatio: true
  }"
>
  图片或其他需要保持比例的内容
</div>
```

#### 3. 隐藏手柄

```vue
<div 
  v-resize="{
    showHandles: false,
    handles: ['e', 's', 'se']
  }"
>
  手柄默认隐藏，鼠标悬停时显示
</div>
```

#### 4. 使用API方法

```vue
<template>
  <div ref="resizableRef" v-resize="resizeOptions">
    内容
  </div>
  <button @click="resetSize">重置大小</button>
  <button @click="setCustomSize">设置自定义大小</button>
</template>

<script setup>
import { ref } from 'vue'

const resizableRef = ref(null)
const resizeOptions = {
  minWidth: 200,
  minHeight: 150
}

const resetSize = () => {
  // 重置到默认大小
  resizableRef.value._resizeAPI.setSize(300, 200)
}

const setCustomSize = () => {
  // 设置自定义大小
  resizableRef.value._resizeAPI.setSize(400, 300)
}

const getCurrentSize = () => {
  // 获取当前大小
  const size = resizableRef.value._resizeAPI.getSize()
  console.log('当前大小:', size)
}
</script>
```

### API 方法

通过元素的 `_resizeAPI` 属性可以访问以下方法：

- `setSize(width, height)`: 设置元素大小
- `getSize()`: 获取当前元素大小，返回 `{width, height}`
- `updateConfig(newOptions)`: 更新配置选项

### 回调函数参数

`onResize` 回调函数接收一个对象参数，包含以下属性：

```javascript
{
  width: 300,        // 新的宽度
  height: 200,       // 新的高度
  direction: 'se',   // 拖拽方向
  element: HTMLElement // 被调整大小的元素
}
```

### 样式自定义

可以通过CSS自定义手柄样式：

```css
/* 自定义手柄样式 */
.resize-handle {
  transition: background-color 0.2s ease;
}

.resize-handle-se {
  border-bottom-right-radius: 4px;
}

.resize-handle:hover {
  background-color: rgba(0, 123, 255, 0.5) !important;
}
```

### 注意事项

1. 元素需要设置 `position: relative` 或其他非 `static` 定位
2. 建议为元素设置初始的 `width` 和 `height`
3. 在使用 `preserveAspectRatio` 时，建议只使用角落手柄（ne, se, sw, nw）
4. 手柄会自动添加到元素内部，确保元素有足够的空间显示手柄

### 浏览器兼容性

- 现代浏览器（Chrome, Firefox, Safari, Edge）
- 支持触摸设备
- 需要支持 Pointer Events API
