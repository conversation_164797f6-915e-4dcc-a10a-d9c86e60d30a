// 拖拽调整大小指令（Vue 3）
// 用法：app.use(resizeDirective) 后，<div v-resize="options">...</div>
export default {
  install(app) {
    app.directive('resize', {
      mounted(el, binding) {
        const options = binding.value || {}
        
        // 默认配置
        const config = {
          minWidth: options.minWidth || 100,
          minHeight: options.minHeight || 100,
          maxWidth: options.maxWidth || null,
          maxHeight: options.maxHeight || null,
          handles: options.handles || ['se', 'e', 's'], // 支持的拖拽手柄
          handleSize: options.handleSize || 8,
          onResize: options.onResize || null,
          preserveAspectRatio: options.preserveAspectRatio || false,
          showHandles: options.showHandles !== false // 默认显示手柄
        }

        // 设置元素样式
        el.style.position = el.style.position || 'relative'
        el.style.boxSizing = 'border-box'

        // 创建拖拽手柄
        const handles = {}
        const createHandle = (direction) => {
          const handle = document.createElement('div')
          handle.className = `resize-handle resize-handle-${direction}`
          handle.style.position = 'absolute'
          handle.style.backgroundColor = config.showHandles ? 'rgba(0, 123, 255, 0.2)' : 'transparent'
          handle.style.zIndex = '1000'
          handle.style.transition = 'background-color 0.2s ease'
          
          // 根据方向设置手柄位置和样式
          switch (direction) {
            case 'se': // 右下角
              handle.style.right = '0'
              handle.style.bottom = '0'
              handle.style.width = config.handleSize + 'px'
              handle.style.height = config.handleSize + 'px'
              handle.style.cursor = 'se-resize'
              break
            case 'e': // 右边
              handle.style.right = '0'
              handle.style.top = '0'
              handle.style.width = config.handleSize + 'px'
              handle.style.height = '100%'
              handle.style.cursor = 'e-resize'
              break
            case 's': // 下边
              handle.style.bottom = '0'
              handle.style.left = '0'
              handle.style.width = '100%'
              handle.style.height = config.handleSize + 'px'
              handle.style.cursor = 's-resize'
              break
            case 'n': // 上边
              handle.style.top = '0'
              handle.style.left = '0'
              handle.style.width = '100%'
              handle.style.height = config.handleSize + 'px'
              handle.style.cursor = 'n-resize'
              break
            case 'w': // 左边
              handle.style.left = '0'
              handle.style.top = '0'
              handle.style.width = config.handleSize + 'px'
              handle.style.height = '100%'
              handle.style.cursor = 'w-resize'
              break
            case 'ne': // 右上角
              handle.style.right = '0'
              handle.style.top = '0'
              handle.style.width = config.handleSize + 'px'
              handle.style.height = config.handleSize + 'px'
              handle.style.cursor = 'ne-resize'
              break
            case 'nw': // 左上角
              handle.style.left = '0'
              handle.style.top = '0'
              handle.style.width = config.handleSize + 'px'
              handle.style.height = config.handleSize + 'px'
              handle.style.cursor = 'nw-resize'
              break
            case 'sw': // 左下角
              handle.style.left = '0'
              handle.style.bottom = '0'
              handle.style.width = config.handleSize + 'px'
              handle.style.height = config.handleSize + 'px'
              handle.style.cursor = 'sw-resize'
              break
          }

          // 鼠标悬停效果
          handle.addEventListener('mouseenter', () => {
            handle.style.backgroundColor = 'rgba(0, 123, 255, 0.5)'
          })
          handle.addEventListener('mouseleave', () => {
            handle.style.backgroundColor = config.showHandles ? 'rgba(0, 123, 255, 0.2)' : 'transparent'
          })

          el.appendChild(handle)
          handles[direction] = handle
          return handle
        }

        // 创建指定的手柄
        config.handles.forEach(direction => {
          createHandle(direction)
        })

        // 拖拽逻辑
        let isResizing = false
        let currentHandle = null
        let startX = 0
        let startY = 0
        let startWidth = 0
        let startHeight = 0
        let startLeft = 0
        let startTop = 0

        const onMouseDown = (e, direction) => {
          e.preventDefault()
          e.stopPropagation()
          
          isResizing = true
          currentHandle = direction
          startX = e.clientX
          startY = e.clientY
          
          const rect = el.getBoundingClientRect()
          const computedStyle = window.getComputedStyle(el)
          startWidth = parseFloat(computedStyle.width)
          startHeight = parseFloat(computedStyle.height)
          startLeft = parseFloat(computedStyle.left) || 0
          startTop = parseFloat(computedStyle.top) || 0

          document.addEventListener('mousemove', onMouseMove)
          document.addEventListener('mouseup', onMouseUp)
          document.body.style.userSelect = 'none'
          document.body.style.cursor = e.target.style.cursor
        }

        const onMouseMove = (e) => {
          if (!isResizing) return

          const deltaX = e.clientX - startX
          const deltaY = e.clientY - startY
          
          let newWidth = startWidth
          let newHeight = startHeight
          let newLeft = startLeft
          let newTop = startTop

          // 根据拖拽方向计算新尺寸
          switch (currentHandle) {
            case 'se':
              newWidth = startWidth + deltaX
              newHeight = startHeight + deltaY
              break
            case 'e':
              newWidth = startWidth + deltaX
              break
            case 's':
              newHeight = startHeight + deltaY
              break
            case 'n':
              newHeight = startHeight - deltaY
              newTop = startTop + deltaY
              break
            case 'w':
              newWidth = startWidth - deltaX
              newLeft = startLeft + deltaX
              break
            case 'ne':
              newWidth = startWidth + deltaX
              newHeight = startHeight - deltaY
              newTop = startTop + deltaY
              break
            case 'nw':
              newWidth = startWidth - deltaX
              newHeight = startHeight - deltaY
              newLeft = startLeft + deltaX
              newTop = startTop + deltaY
              break
            case 'sw':
              newWidth = startWidth - deltaX
              newHeight = startHeight + deltaY
              newLeft = startLeft + deltaX
              break
          }

          // 保持宽高比
          if (config.preserveAspectRatio) {
            const aspectRatio = startWidth / startHeight
            if (currentHandle.includes('e') || currentHandle.includes('w')) {
              newHeight = newWidth / aspectRatio
            } else if (currentHandle.includes('n') || currentHandle.includes('s')) {
              newWidth = newHeight * aspectRatio
            }
          }

          // 应用最小/最大尺寸限制
          newWidth = Math.max(config.minWidth, newWidth)
          newHeight = Math.max(config.minHeight, newHeight)
          
          if (config.maxWidth) newWidth = Math.min(config.maxWidth, newWidth)
          if (config.maxHeight) newHeight = Math.min(config.maxHeight, newHeight)

          // 应用新尺寸
          el.style.width = newWidth + 'px'
          el.style.height = newHeight + 'px'

          // 如果需要调整位置（针对 n, w, nw, ne, sw 方向）
          if (currentHandle.includes('n') || currentHandle.includes('w')) {
            const position = window.getComputedStyle(el).position
            if (position === 'absolute' || position === 'fixed') {
              if (currentHandle.includes('w')) {
                el.style.left = newLeft + 'px'
              }
              if (currentHandle.includes('n')) {
                el.style.top = newTop + 'px'
              }
            }
          }

          // 触发回调
          if (config.onResize) {
            config.onResize({
              width: newWidth,
              height: newHeight,
              direction: currentHandle,
              element: el
            })
          }
        }

        const onMouseUp = () => {
          isResizing = false
          currentHandle = null
          document.removeEventListener('mousemove', onMouseMove)
          document.removeEventListener('mouseup', onMouseUp)
          document.body.style.userSelect = ''
          document.body.style.cursor = ''
        }

        // 为每个手柄绑定事件
        Object.entries(handles).forEach(([direction, handle]) => {
          handle.addEventListener('mousedown', (e) => onMouseDown(e, direction))
        })

        // 保存清理函数和配置
        el._resizeCleanup = () => {
          Object.values(handles).forEach(handle => {
            if (handle.parentNode) {
              handle.parentNode.removeChild(handle)
            }
          })
          document.removeEventListener('mousemove', onMouseMove)
          document.removeEventListener('mouseup', onMouseUp)
        }

        // 保存API方法
        el._resizeAPI = {
          updateConfig(newOptions) {
            Object.assign(config, newOptions)
          },
          getSize() {
            return {
              width: parseFloat(window.getComputedStyle(el).width),
              height: parseFloat(window.getComputedStyle(el).height)
            }
          },
          setSize(width, height) {
            if (width !== undefined) el.style.width = width + 'px'
            if (height !== undefined) el.style.height = height + 'px'
          }
        }
      },

      unmounted(el) {
        if (el._resizeCleanup) {
          el._resizeCleanup()
          delete el._resizeCleanup
        }
        if (el._resizeAPI) {
          delete el._resizeAPI
        }
      }
    })
  }
}
